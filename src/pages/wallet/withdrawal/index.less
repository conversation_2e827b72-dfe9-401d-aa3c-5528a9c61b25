@import '@arco-design/mobile-react/style/mixin.less';

[id^="/pages/wallet/withdrawal/index"] {
  background-color: @card-background-color !important;
  .use-dark-mode-query({
    background-color: @dark-card-background-color !important;
  });
}

.withdrawal {
  position: relative;
  width: 100%;
  min-height: 100vh;
  background-color: #F7F8FA; // 填充 Fill/填充fill-1
  .use-dark-mode-query({
    background-color: @dark-card-background-color;
  });

  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 50vh;
  }

  .withdrawal-content {
    display: flex;
    flex-direction: column;
    min-height: calc(100vh - 84px); // 减去导航栏高度
  }

  // 收款账户信息
  .account-info {
    background-color: #FFFFFF; // 填充 Fill/Container 容器背景色
    .use-dark-mode-query({
        background-color: @dark-container-background-color;
    });
    padding: 20px 15px;
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 15px;

    .account-info-header {
      display: flex;
      align-items: center;
      gap: 5px;

      .account-info-label {
        font-family: 'PingFangSC-Medium';
        font-size: 14px;
        line-height: 140%;
        color: #4E5969; // 文字 Text/文字-4-副标- Grey 8
        .use-dark-mode-query({
            color: @dark-sub-font-color;
        });
        opacity: 0.9;
      }

      .account-info-edit {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 16px;
        height: 16px;
        cursor: pointer;


        color: #fff !important; // 使用 !important 强制覆盖
        .use-dark-mode-query({
            fill: @dark-sub-font-color !important; // 在暗黑模式下也使用 !important
        });

      }
    }

    .account-info-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 8px;

      .account-name {
        font-family: 'PingFangSC-Medium';
        font-size: 14px;
        line-height: 140%;
        color: #1D2129; // 文字 Text/文字-5-基础  Grey 10
        .use-dark-mode-query({
            color: @dark-font-color;
        });
        opacity: 0.9;
      }

      .account-number {
        font-family: 'PingFangSC-Medium';
        font-size: 14px;
        line-height: 140%;
        color: #4E5969; // 文字 Text/文字-4-副标- Grey 8
        .use-dark-mode-query({
            color: @dark-sub-font-color;
        });
        opacity: 0.9;
      }
    }
  }

  .account-info-loading {
    background-color: #FFFFFF;
    .use-dark-mode-query({
        background-color: @dark-container-background-color;
    });
    padding: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  // 提现表单
  .withdrawal-form {
    background-color: #FFFFFF; // 填充 Fill/Container 容器背景色
    .use-dark-mode-query({
        background-color: @dark-container-background-color;
    });
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 0 20px;
    margin-top: 10px;

    .amount-input-section {
      padding: 20px 0;

      .amount-input-header {
        margin-bottom: 25px;

        .amount-input-label {
          font-family: 'PingFangSC-Medium';
          font-size: 14px;
          line-height: 140%;
          color: #1D2129; // 文字 Text/文字-5-基础  Grey 10
          .use-dark-mode-query({
            color: @dark-font-color;
          });
          opacity: 0.9;
        }
      }

      .amount-input-container {
        margin-bottom: 5px;

        .amount-input-wrapper {
          display: flex;
          align-items: center;
          border-bottom: 0.5px solid #E5E6EB; // 线条 Line/线条line-1-基础
          .use-dark-mode-query({
            border-color: @dark-line-color;
          });
          padding-bottom: 10px;

          .currency-symbol {
            font-family: 'PingFangSC-Medium';
            font-size: 16px;
            line-height: 140%;
            color: #1D2129; // 文字 Text/文字-5-基础  Grey 10
            .use-dark-mode-query({
                color: @dark-font-color;
            });
            margin-right: 16px;
          }

          .amount-input {
            flex: 1;
            border: none;
            outline: none;
            background: transparent;
            font-family: 'PingFangSC-Medium';
            font-size: 15px;
            line-height: 140%;
            color: #1D2129;
            .use-dark-mode-query({
                color: @dark-font-color;
            });

            &::placeholder {
              color: #C9CDD4; // 文字 Text/文字-2-禁用- Grey 4
              .use-dark-mode-query({
                color: @dark-disabled-color;
              });
            }
          }
        }
      }

      .error-message {
        margin-top: 4px;
        font-size: 12px;
        line-height: 16px;
        color: #F53F3F;
        .use-dark-mode-query({
            color: @dark-danger-color;
        });
      }

      .amount-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 0;

        .available-amount {
          font-family: 'PingFangSC-Medium';
          font-size: 14px;
          line-height: 140%;
          color: #4E5969; // 文字 Text/文字-4-副标- Grey 8
          .use-dark-mode-query({
            color: @dark-sub-font-color;
          });
          opacity: 0.9;
        }

        .withdraw-all {
          font-family: 'PingFangSC-Medium';
          font-size: 14px;
          line-height: 140%;
          color: #002C8C; // Primary/colorPrimaryText
          .use-dark-mode-query({
            color: @dark-primary-color;
          });
          cursor: pointer;
        }
      }
    }

    .withdrawal-submit {
      padding: 20px 0;

      .disabled-button {
        opacity: 0.6;
      }
    }

    .withdrawal-tips {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 20px 0;

      .tips-text {
        font-family: 'PingFangSC-Medium';
        font-size: 14px;
        line-height: 140%;
        color: #86909C; // 文字 Text/文字-3-附加信息-Grey 6
        .use-dark-mode-query({
            color: @dark-sub-info-font-color;
        });
        text-align: center;
      }
    }
  }

  // 确认提现弹窗样式 - 基于DSL设计
  .withdrawal-confirm-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
    display: flex;
    justify-content: center;
    align-items: center;

    .modal-mask {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.6); // 填充 Fill/Mask 蒙层基础背景色
    }

    .modal-dialog {
      position: relative;
      width: 258px;
      height: 268px;
      background-color: #FFFFFF;
      .use-dark-mode-query({
        background-color: @dark-container-background-color;
      });
      border-radius: 4px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding: 25px 22px;

      // 弹窗标题栏
      .dialog-header {
        position: absolute;
        top: 25px;
        left: 22px;
        right: 22px;
        height: 20px;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;

        .dialog-close-icon {
          position: absolute;
          left: 0;
          top: 0;
          width: 20px;
          height: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;

          .close-icon-svg {
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;

            svg {
              width: 20px;
              height: 20px;
            }

            path {
              fill: #4E5969; // 文字 Text/文字-4-副标- Grey 8
              .use-dark-mode-query({
                fill: @dark-sub-font-color;
              });
            }
          }
        }

        .dialog-title-container {
          flex: 1;
          display: flex;
          justify-content: center;
          align-items: center;

          .dialog-title {
            font-family: 'PingFangSC-Medium';
            font-size: 14px;
            line-height: 140%;
            color: #1D2129; // 文字 Text/文字-5-基础  Grey 10
            .use-dark-mode-query({
              color: @dark-font-color;
            });
            text-align: center;
          }
        }
      }

      // 金额显示区域
      .dialog-amount-container {
        width: 103px;
        height: 74px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 5px;
        padding: 20px 0;
        margin-top: 20px;

        .dialog-amount-text {
          font-family: 'PingFang SC';
          font-size: 24px;
          font-weight: bold;
          line-height: 140%;
          color: #1D2129; // 文字 Text/文字-5-基础  Grey 10
          .use-dark-mode-query({
            color: @dark-font-color;
          });
          text-align: left;
        }
      }

      // 手续费信息区域
      .dialog-fee-container {
        width: 214px;
        height: 60px;
        border-top: 0.5px solid rgba(153, 153, 153, 0.2);
        .use-dark-mode-query({
          border-color: fade(@dark-line-color, 20%);
        });
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        gap: 5px;
        padding: 20px 0;

        .dialog-fee-row {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;
          width: 100%;

          .dialog-fee-label {
            font-family: 'PingFangSC-Medium';
            font-size: 14px;
            line-height: 140%;
            color: #4E5969; // 文字 Text/文字-4-副标- Grey 8
            .use-dark-mode-query({
              color: @dark-sub-font-color;
            });
            opacity: 0.9;
            text-align: left;
            flex-grow: 1;
          }

          .dialog-fee-value {
            font-family: 'PingFangSC-Medium';
            font-size: 14px;
            line-height: 140%;
            color: #1D2129; // 文字 Text/文字-5-基础  Grey 10
            .use-dark-mode-query({
              color: @dark-font-color;
            });
            opacity: 0.9;
            text-align: left;
          }
        }
      }

      // 确认按钮
      .dialog-button-container {
        width: 214px;
        height: 64px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        .dialog-confirm-button {
          width: 182px;
          height: 32px;
          background-color: #165DFF; // Primary color
          .use-dark-mode-query({
            background-color: @dark-primary-color;
          });
          border: none;
          border-radius: 4px;

          .arco-btn-text {
            font-family: 'PingFangSC-Regular';
            font-size: 14px;
            line-height: 140%;
            color: #FFFFFF; // 填充 Fill/Mask 蒙层内容字体颜色
            text-align: left;
          }
        }
      }
    }
  }
}

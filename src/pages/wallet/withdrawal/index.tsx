import { View, Text } from "@tarojs/components";
import "./index.less";
import Taro from "@tarojs/taro";
import { useState, useEffect } from "react";
import {
  Input,
  Button,
  Toast,
  Loading,
  Keyboard
} from "@arco-design/mobile-react";

// 组件
import YkNavBar from "@/components/ykNavBar/index";
import { IconEdit } from '@arco-design/mobile-react/esm/icon';

// API
import {
  withdrawal,
  getBalance,
  getAccount
} from "@/utils/api/common/common_wechat";

// 工具函数
import { isDebugMode, getDebugScenario, WalletDebugScenario } from '../utils';

// 类型定义
interface AccountInfo {
  accountName: string;
  accountNumber: string;
  bankName?: string;
}

interface BalanceInfo {
  availableAmount: number;
}

interface PageState {
  loading: boolean;
  submitting: boolean;
  accountInfo: AccountInfo | null;
  balanceInfo: BalanceInfo | null;
  showConfirmDialog: boolean;
}

export default function Withdrawal() {
  // ==================== 状态管理 ====================
  const [amount, setAmount] = useState('');

  const [pageState, setPageState] = useState<PageState>({
    loading: true,
    submitting: false,
    accountInfo: null,
    balanceInfo: null,
    showConfirmDialog: false
  });

  const [keyboardVisible, setKeyboardVisible] = useState(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  // ==================== 生命周期 ====================
  useEffect(() => {
    initPage();
  }, []);

  // ==================== 页面初始化 ====================
  const initPage = async () => {
    setPageState(prev => ({ ...prev, loading: true }));
    try {
      await Promise.all([
        loadAccountInfo(),
        loadBalanceInfo()
      ]);
    } catch (error) {
      console.error('页面初始化失败:', error);
      Toast.error('页面加载失败');
    } finally {
      setPageState(prev => ({ ...prev, loading: false }));
    }
  };

  // ==================== 数据加载 ====================
  const loadAccountInfo = async () => {
    try {
      // 调试模式下使用模拟数据
      if (isDebugMode()) {
        const mockData = {
          accountName: '福建朱雀科技有限公司',
          accountNumber: '6391****3333',
          bankName: '中国工商银行'
        };
        setPageState(prev => ({ ...prev, accountInfo: mockData }));
        return;
      }

      const response = await getAccount();
      if (response.code === 0) {
        setPageState(prev => ({
          ...prev,
          accountInfo: {
            accountName: response.data.accountName || '',
            accountNumber: response.data.accountNumber || '',
            bankName: response.data.bankName || ''
          }
        }));
      }
    } catch (error) {
      console.error('获取账户信息失败:', error);
    }
  };

  const loadBalanceInfo = async () => {
    try {
      // 调试模式下使用模拟数据
      if (isDebugMode()) {
        const scenario = getDebugScenario();
        const mockData = {
          availableAmount: scenario === WalletDebugScenario.NOBALANCE ? 0 : 100.50
        };
        setPageState(prev => ({ ...prev, balanceInfo: mockData }));
        return;
      }

      const response = await getBalance();
      if (response.code === 0) {
        setPageState(prev => ({
          ...prev,
          balanceInfo: {
            availableAmount: response.data.availableAmount || 0
          }
        }));
      }
    } catch (error) {
      console.error('获取余额信息失败:', error);
    }
  };

  // ==================== 表单验证 ====================
  const validateForm = (): boolean => {
    const newErrors: { [key: string]: string } = {};

    if (!amount) {
      newErrors.amount = '请输入提现金额';
      setErrors(newErrors);
      return false;
    }

    const amountValue = parseFloat(amount);
    if (isNaN(amountValue) || amountValue <= 0) {
      newErrors.amount = '请输入有效的提现金额';
      setErrors(newErrors);
      return false;
    }

    if (amountValue < 1) {
      newErrors.amount = '提现金额不能少于1元';
      setErrors(newErrors);
      return false;
    }

    if (pageState.balanceInfo && amountValue > pageState.balanceInfo.availableAmount) {
      newErrors.amount = '提现金额不能超过可提现余额';
      setErrors(newErrors);
      return false;
    }

    setErrors({});
    return true;
  };

  // ==================== 事件处理 ====================
  const handleInputFocus = () => {
    setKeyboardVisible(true);
  };

  const handleKeyboardInput = (char: string) => {
    if (char === "delete") {
      setAmount((prev) => prev.slice(0, -1));
    } else if (char === "close") {
      setKeyboardVisible(false);
    } else {
      const newAmount = amount + char;

      if (char === "." && amount.includes(".")) {
        return;
      }

      const parts = newAmount.split(".");
      if (parts.length > 2 || (parts[1] && parts[1].length > 2)) {
        return;
      }

      setAmount(newAmount);

      if (errors.amount) {
        setErrors((prev) => ({ ...prev, amount: "" }));
      }
    }
  };

  const handleWithdrawAll = () => {
    if (pageState.balanceInfo) {
      setAmount(pageState.balanceInfo.availableAmount.toFixed(2));
      if (errors.amount) {
        setErrors(prev => ({ ...prev, amount: '' }));
      }
    }
  };

  const handleEditAccount = () => {
    Taro.navigateTo({
      url: '/pages/wallet/withdrawal/updateAccount'
    });
  };

  const handleConfirmWithdrawal = () => {
    if (!validateForm()) return;
    setKeyboardVisible(false);
    setPageState(prev => ({ ...prev, showConfirmDialog: true }));
  };

  const handleSubmitWithdrawal = async () => {
    setPageState(prev => ({ ...prev, submitting: true, showConfirmDialog: false }));

    try {
      const amountValue = parseFloat(amount);
      const withdrawalData = {
        amount: Math.round(amountValue * 100),
        remark: '用户提现',
        account_type: 'ACCOUNT_TYPE_BUSINESS'
      };

      if (isDebugMode()) {
        await new Promise(resolve => setTimeout(resolve, 2000));
        Toast.success('提现申请已提交');
        Taro.navigateTo({
          url: `/pages/wallet/withdrawal/result?status=success&amount=${amountValue}`
        });
        return;
      }

      const response = await withdrawal(withdrawalData);
      if (response.code === 0) {
        Toast.success('提现申请已提交');
        Taro.navigateTo({
          url: `/pages/wallet/withdrawal/result?status=success&amount=${amountValue}`
        });
      } else {
        Toast.error(response.msg || '提现申请失败');
      }
    } catch (error) {
      console.error('提现失败:', error);
      Toast.error('提现申请失败，请稍后重试');
    } finally {
      setPageState(prev => ({ ...prev, submitting: false }));
    }
  };

  const handleCancelDialog = () => {
    setPageState(prev => ({ ...prev, showConfirmDialog: false }));
  };

  // ==================== 渲染函数 ====================
  const renderAccountInfo = () => {
    if (!pageState.accountInfo) {
      return (
        <View className="account-info-loading">
          <Loading />
        </View>
      );
    }

    return (
      <View className="account-info">
        <View className="account-info-header">
          <Text className="account-info-label">收款账户</Text>
        </View>
        <View className="account-info-content">
          <Text className="account-name">{pageState.accountInfo.accountName}</Text>
          <Text className="account-number">{pageState.accountInfo.accountNumber}</Text>
        </View>
        <View className="account-info-edit" onClick={handleEditAccount}>
            <IconEdit useCurrentColor />
          </View>
      </View>
    );
  };

  const renderAmountInput = () => {
    const availableAmount = pageState.balanceInfo?.availableAmount || 0;

    return (
      <View className="amount-input-section">
        <View className="amount-input-header">
          <Text className="amount-input-label">提现金额</Text>
        </View>

        <View className="amount-input-container">
          <View className="amount-input-wrapper">
            <Text className="currency-symbol">¥</Text>
            <Input
              className="amount-input"
              placeholder="1元起提"
              value={amount}
              onFocus={handleInputFocus}
              readOnly
              border="none"
            />
          </View>
        </View>

        {errors.amount && (
          <Text className="error-message">{errors.amount}</Text>
        )}

        <View className="amount-info">
          <Text className="available-amount">
            可提现金额  ¥{availableAmount.toFixed(2)}
          </Text>
          <Text className="withdraw-all" onClick={handleWithdrawAll}>
            全部提现
          </Text>
        </View>
      </View>
    );
  };

  return (
    <View className="withdrawal">
      <YkNavBar title="提现" />

      {pageState.loading ? (
        <View className="loading-container">
          <Loading />
        </View>
      ) : (
        <View className="withdrawal-content">
          {renderAccountInfo()}

          <View className="withdrawal-form">
            {renderAmountInput()}

            <View className="withdrawal-submit">
              <Button
                type="primary"
                size="large"
                loading={pageState.submitting}
                disabled={pageState.submitting || !amount}
                onClick={handleConfirmWithdrawal}
                className={!amount ? "disabled-button" : ""}
              >
                确认提现
              </Button>
            </View>

            <View className="withdrawal-tips">
              <Text className="tips-text">
                建议留存部分资金，用于处理售后退款
              </Text>
            </View>
          </View>
        </View>
      )}

      {/* 确认提现弹窗 - 基于DSL设计 */}
      {pageState.showConfirmDialog && (
        <View className="withdrawal-confirm-modal">
          <View className="modal-mask" onClick={handleCancelDialog}></View>
          <View className="modal-dialog">
            {/* 弹窗标题栏 */}
            <View className="dialog-header">
              <View className="dialog-close-icon" onClick={handleCancelDialog}>
                <View className="close-icon-svg">
                  <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                    <path
                      d="M6.464 6.464a.5.5 0 0 1 .707 0L10 9.293l2.829-2.829a.5.5 0 1 1 .707.707L10.707 10l2.829 2.829a.5.5 0 0 1-.707.707L10 10.707l-2.829 2.829a.5.5 0 0 1-.707-.707L9.293 10 6.464 7.171a.5.5 0 0 1 0-.707z"
                      fill="#4E5969"
                    />
                  </svg>
                </View>
              </View>
              <View className="dialog-title-container">
                <Text className="dialog-title">提现金额</Text>
              </View>
            </View>

            {/* 金额显示区域 */}
            <View className="dialog-amount-container">
              <Text className="dialog-amount-text">¥{amount}</Text>
            </View>

            {/* 手续费信息区域 */}
            <View className="dialog-fee-container">
              <View className="dialog-fee-row">
                <Text className="dialog-fee-label">手续费</Text>
                <Text className="dialog-fee-value">本次提现免费</Text>
              </View>
            </View>

            {/* 确认按钮 */}
            <View className="dialog-button-container">
              <Button
                type="primary"
                size="large"
                loading={pageState.submitting}
                onClick={handleSubmitWithdrawal}
                className="dialog-confirm-button"
              >
                确认提现
              </Button>
            </View>
          </View>
        </View>
      )}

      <Keyboard
        visible={keyboardVisible}
        randomOrder={false}
        close={() => {
          setKeyboardVisible(false);
          return {};
        }}
        onDelete={() => {
          handleKeyboardInput("delete");
          return {};
        }}
        onChange={(data) => {
          handleKeyboardInput(String(data));
          return {};
        }}
        type="number"
        title={
          <div
            style={{
              fontSize: 13,
              lineHeight: "18px",
              color: "#86909C",
              marginBottom: 8,
              textAlign: "center",
            }}
          >
            安全信息提示
          </div>
        }
      />
    </View>
  );
}
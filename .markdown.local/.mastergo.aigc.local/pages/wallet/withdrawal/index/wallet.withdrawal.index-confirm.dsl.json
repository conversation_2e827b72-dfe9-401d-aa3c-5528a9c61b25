{"dsl": {"styles": {"paint_3:08475": {"value": ["rgba(0, 0, 0, 0.6)"], "token": "填充 Fill/Mask 蒙层基础背景色"}, "effect_408:039881": {"value": []}, "paint_98:100269": {"value": ["#FFFFFF"]}, "paint_3:9743": {"value": ["#4E5969"], "token": "文字 Text/文字-4-副标- Grey 8"}, "paint_3:9736": {"value": ["#1D2129"], "token": "文字 Text/文字-5-基础  Grey 10"}, "font_3:01136": {"value": {"family": "PingFangSC-Medium", "size": 14, "decoration": "none", "case": "none", "lineHeight": "140", "letterSpacing": "auto"}, "token": "14/Medium"}, "font_10:78018": {"value": {"family": "PingFang SC", "size": 24, "style": "{\"fontStyle\":\"Bold\",\"opsz\":\"auto\"}", "decoration": "none", "case": "none", "lineHeight": "140", "letterSpacing": "auto"}, "token": "24/Bold"}, "paint_3:46804": {"value": ["rgba(153, 153, 153, 0.2)"]}, "paint_3:08546": {"value": ["#FFFFFF"], "token": "填充 Fill/Mask 蒙层内容字体颜色"}, "font_3:07643": {"value": {"family": "PingFangSC-Regular", "size": 14, "decoration": "none", "case": "none", "lineHeight": "140", "letterSpacing": "auto"}, "token": "14/Regular"}}, "nodes": [{"type": "FRAME", "id": "450:81945", "name": "弹窗：确认提现", "layoutStyle": {"width": 375, "height": 812, "relativeX": 0, "relativeY": 0}, "fill": "paint_3:08475", "children": [{"type": "FRAME", "id": "450:81946", "name": "对话框 1", "layoutStyle": {"width": 258, "height": 268, "relativeX": 58.5, "relativeY": 272}, "fill": "paint_98:100269", "children": [{"type": "FRAME", "id": "450:81947", "name": "弹窗标题", "layoutStyle": {"width": 214, "height": 20, "relativeX": 22, "relativeY": 25}, "flexShrink": 1, "children": [{"type": "INSTANCE", "id": "450:82228", "name": "图标：展开", "layoutStyle": {"width": 20, "height": 20}, "componentInfo": {"name": "尺寸[a5]=20", "properties": {"尺寸": "20"}}, "children": [{"type": "INSTANCE", "id": "450:82228/411:85834", "layoutStyle": {"width": 20, "height": 20}, "componentInfo": {"name": "符号-叉-关闭-close"}, "children": [{"type": "PATH", "id": "450:82228/411:85834/3:08167", "name": "Vector", "layoutStyle": {"width": 12.963624954223633, "height": 12.963624954223633, "relativeX": 3.75, "relativeY": 3.3333332538604736}, "path": [{"fill": "paint_3:9743", "data": "M12.7279 15.5563C12.7279 15.5563 1.19209e-06 2.82843 1.19209e-06 2.82843C1.19209e-06 2.82843 2.82843 0 2.82843 0C2.82843 0 15.5563 12.7279 15.5563 12.7279C15.5563 12.7279 28.2843 0 28.2843 0C28.2843 0 31.1127 2.82843 31.1127 2.82843C31.1127 2.82843 18.3848 15.5563 18.3848 15.5563C18.3848 15.5563 31.1127 28.2843 31.1127 28.2843C31.1127 28.2843 28.2843 31.1127 28.2843 31.1127C28.2843 31.1127 15.5563 18.3848 15.5563 18.3848C15.5563 18.3848 2.82843 31.1127 2.82843 31.1127C2.82843 31.1127 0 28.2843 0 28.2843C0 28.2843 12.7279 15.5563 12.7279 15.5563C12.7279 15.5563 12.7279 15.5563 12.7279 15.5563Z"}]}], "componentId": "3:08166"}], "componentId": "411:85833"}, {"type": "FRAME", "id": "450:81952", "name": "右侧", "layoutStyle": {"width": 169, "height": 20, "relativeX": 20}, "flexGrow": 1, "children": [{"type": "TEXT", "id": "450:81953", "name": "主标题", "layoutStyle": {"width": 56, "height": 20, "relativeX": 56.5}, "effect": "effect_408:039881", "text": [{"text": "提现金额", "font": "font_3:01136"}], "textColor": [{"start": 0, "end": 4, "color": "paint_3:9736"}], "textAlign": "center", "textMode": "single-line"}], "flexContainerInfo": {"flexDirection": "row", "justifyContent": "center", "alignItems": "center", "mainSizing": "fixed", "crossSizing": "auto"}}, {"type": "FRAME", "id": "450:81948", "name": "组 212", "layoutStyle": {"width": 25, "height": 16, "relativeX": 189, "relativeY": 2}, "children": [], "flexContainerInfo": {"flexDirection": "row", "justifyContent": "center", "alignItems": "center", "mainSizing": "fixed", "crossSizing": "auto"}}], "flexContainerInfo": {"flexDirection": "row", "justifyContent": "center", "alignItems": "center", "mainSizing": "fixed", "crossSizing": "auto"}}, {"type": "FRAME", "id": "450:81956", "name": "容器 79", "layoutStyle": {"width": 103, "height": 74, "relativeX": 77.5, "relativeY": 45}, "children": [{"type": "TEXT", "id": "450:81957", "name": "￥100.50", "layoutStyle": {"width": 103, "height": 34, "relativeY": 20}, "effect": "effect_408:039881", "text": [{"text": "￥100.50", "font": "font_10:78018"}], "textColor": [{"start": 0, "end": 7, "color": "paint_3:9736"}], "textAlign": "left", "textMode": "single-line"}], "flexContainerInfo": {"flexDirection": "column", "justifyContent": "center", "alignItems": "center", "mainSizing": "auto", "crossSizing": "auto", "gap": "5px", "padding": "20px 0px"}}, {"type": "FRAME", "id": "450:81960", "name": "容器 1353", "layoutStyle": {"width": 214, "height": 60, "relativeX": 22, "relativeY": 119}, "flexShrink": 1, "strokeColor": "paint_3:46804", "strokeType": "dashed", "strokeAlign": "outside", "strokeWidth": "0.5px 0px 0px", "children": [{"type": "TEXT", "id": "450:81961", "name": "隐私政策文本", "layoutStyle": {"width": 125, "height": 20, "relativeY": 20}, "flexGrow": 1, "opacity": 0.9000000357627869, "effect": "effect_408:039881", "text": [{"text": "手续费", "font": "font_3:01136"}], "textColor": [{"start": 0, "end": 3, "color": "paint_3:9743"}], "textAlign": "left", "textMode": "auto-height"}, {"type": "TEXT", "id": "450:81964", "name": "隐私政策文本", "layoutStyle": {"width": 84, "height": 20, "relativeX": 130, "relativeY": 20}, "opacity": 0.9000000357627869, "effect": "effect_408:039881", "text": [{"text": "本次提现免费", "font": "font_3:01136"}], "textColor": [{"start": 0, "end": 6, "color": "paint_3:9736"}], "textAlign": "left", "textMode": "single-line"}], "flexContainerInfo": {"flexDirection": "row", "mainSizing": "fixed", "crossSizing": "auto", "gap": "5px", "padding": "20px 0px"}}, {"type": "INSTANCE", "id": "450:82295", "layoutStyle": {"width": 214, "height": 64, "relativeX": 22, "relativeY": 179}, "flexShrink": 1, "componentInfo": {"name": "数量[a0]=1,类型[a1]=块级", "properties": {"数量": "1", "类型": "块级"}}, "children": [{"type": "INSTANCE", "id": "450:82295/3:20764", "layoutStyle": {"width": 182, "height": 32, "relativeX": 16, "relativeY": 16}, "flexShrink": 1, "componentInfo": {"name": "类型[a0]=主要,样式[a0]=实心,尺寸[a2]=中-32,交互[a0]=默认,形状[a0]=默认,仅图标[a0]=false,加载中[a1]=false", "componentSetDescription": "按钮", "properties": {"类型": "主要", "样式": "实心", "尺寸": "中-32", "交互": "默认", "形状": "默认", "仅图标": "false", "加载中": "false", "✍️ 按钮文本": "确认提现"}}, "children": [{"type": "TEXT", "id": "450:82295/3:20764/3:11889", "name": "Button Text", "layoutStyle": {"width": 56, "height": 20, "relativeX": 63, "relativeY": 6}, "text": [{"text": "确认提现", "font": "font_3:07643"}], "textColor": [{"start": 0, "end": 4, "color": "paint_3:08546"}], "textAlign": "left", "textMode": "single-line"}], "componentId": "3:11888"}], "componentId": "3:20763"}], "effect": "effect_408:039881", "flexContainerInfo": {"flexDirection": "column", "justifyContent": "center", "alignItems": "center", "mainSizing": "auto", "crossSizing": "fixed", "padding": "25px 22px"}, "borderRadius": "4px"}], "effect": "effect_408:039881", "flexContainerInfo": {"flexDirection": "column", "justifyContent": "center", "alignItems": "center", "mainSizing": "fixed", "crossSizing": "fixed", "padding": "0px 40px"}}], "components": []}, "componentDocumentLinks": [], "rules": ["token filed must be generated as a variable (colors, shadows, fonts, etc.) and the token field must be displayed in the comment", "\n            componentDocumentLinks is a list of frontend component documentation links used in the DSL layer, designed to help you understand how to use the components.\n            When it exists and is not empty, you need to use mcp__getComponentLink in a for loop to get the URL content of all components in the list, understand how to use the components, and generate code using the components.\n            For example: \n              ```js  \n                const componentDocumentLinks = [\n                  'https://example.com/ant/button.mdx',\n                  'https://example.com/ant/button.mdx'\n                ]\n                for (const url of componentDocumentLinks) {\n                  const componentLink = await mcp__getComponentLink(url);\n                  console.log(componentLink);\n                }\n              ```\n          "]}